const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createPendingNodes() {
  console.log('🧪 Creating test nodes with pending approval status...\n');

  try {
    // Create a test developer user if it doesn't exist
    let developer = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!developer) {
      developer = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'test-developer',
          name: 'Test Developer',
          bio: 'Test developer for approval system testing',
          avatar: '/api/placeholder/64/64',
          emailVerified: true
        }
      });
      console.log('✅ Created test developer user');
    }

    // Sample pending nodes
    const pendingNodes = [
      {
        id: 'pending-text-analyzer',
        name: 'Text Analyzer Pro',
        version: '1.0.0',
        description: 'Advanced text analysis with sentiment detection',
        longDescription: 'A comprehensive text analysis tool that provides sentiment analysis, keyword extraction, and readability scoring.',
        category: 'text-processing',
        tier: 'free',
        price: 0,
        tags: ['text', 'analysis', 'sentiment', 'nlp'],
        code: `
function executeNode(env) {
  const { text } = env.inputs;
  
  // Simple sentiment analysis
  const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful'];
  const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'disappointing'];
  
  const words = text.toLowerCase().split(' ');
  let sentiment = 0;
  
  words.forEach(word => {
    if (positiveWords.includes(word)) sentiment++;
    if (negativeWords.includes(word)) sentiment--;
  });
  
  return {
    sentiment: sentiment > 0 ? 'positive' : sentiment < 0 ? 'negative' : 'neutral',
    score: sentiment,
    wordCount: words.length
  };
}

module.exports = { executeNode };
        `
      },
      {
        id: 'pending-data-validator',
        name: 'Data Validator',
        version: '2.1.0',
        description: 'Validate data formats and schemas',
        longDescription: 'Comprehensive data validation tool supporting JSON schema validation, email validation, and custom rules.',
        category: 'utility',
        tier: 'premium',
        price: 9.99,
        tags: ['validation', 'data', 'schema', 'json'],
        code: `
function executeNode(env) {
  const { data, schema } = env.inputs;
  
  // Simple validation logic
  const errors = [];
  
  if (schema.required) {
    schema.required.forEach(field => {
      if (!data[field]) {
        errors.push(\`Missing required field: \${field}\`);
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors,
    validatedData: data
  };
}

module.exports = { executeNode };
        `
      },
      {
        id: 'pending-image-resizer',
        name: 'Smart Image Resizer',
        version: '1.5.0',
        description: 'Intelligent image resizing with quality optimization',
        longDescription: 'Advanced image processing node that resizes images while maintaining quality and aspect ratio.',
        category: 'image-processing',
        tier: 'free',
        price: 0,
        tags: ['image', 'resize', 'optimization', 'quality'],
        code: `
function executeNode(env) {
  const { imageUrl, width, height, maintainAspectRatio } = env.inputs;
  
  // Simulate image processing
  return {
    processedImageUrl: \`\${imageUrl}?w=\${width}&h=\${height}\`,
    originalDimensions: { width: 1920, height: 1080 },
    newDimensions: { width, height },
    compressionRatio: 0.85
  };
}

module.exports = { executeNode };
        `
      },
      {
        id: 'pending-api-connector',
        name: 'Universal API Connector',
        version: '3.0.0',
        description: 'Connect to any REST API with authentication',
        longDescription: 'Flexible API connector supporting various authentication methods including OAuth, API keys, and basic auth.',
        category: 'integration',
        tier: 'premium',
        price: 14.99,
        tags: ['api', 'rest', 'oauth', 'integration'],
        code: `
function executeNode(env) {
  const { url, method, headers, body, auth } = env.inputs;
  
  // Simulate API call
  return {
    status: 200,
    data: { message: 'API call successful', timestamp: new Date().toISOString() },
    headers: { 'content-type': 'application/json' },
    responseTime: 245
  };
}

module.exports = { executeNode };
        `
      }
    ];

    // Create the pending nodes
    for (const nodeData of pendingNodes) {
      // Check if node already exists
      const existingNode = await prisma.nodePlugin.findUnique({
        where: { id: nodeData.id }
      });

      if (existingNode) {
        console.log(`⏭️  Node ${nodeData.name} already exists, skipping...`);
        continue;
      }

      // Create the node with pending status
      const { code, ...nodeCreateData } = nodeData;
      const node = await prisma.nodePlugin.create({
        data: {
          ...nodeCreateData,
          authorId: developer.id,
          tags: JSON.stringify(nodeData.tags),
          dependencies: JSON.stringify([]),
          permissions: JSON.stringify(['basic']),
          icon: '/api/placeholder/64/64',
          screenshots: JSON.stringify(['/api/placeholder/400/300']),
          downloadUrl: `/api/nodes/${nodeData.id}/download`,
          verified: false,
          featured: false,
          rating: 0,
          reviewCount: 0,
          downloads: 0,
          weeklyDownloads: 0,
          compatibility: JSON.stringify({ minVersion: "1.0.0" }),
          changelog: JSON.stringify([{
            version: nodeData.version,
            date: new Date(),
            changes: ["Initial submission for approval"]
          }]),
          approvalStatus: 'pending' // This is the key part!
        }
      });

      // Create the node code
      await prisma.nodeCode.create({
        data: {
          nodeId: nodeData.id,
          version: nodeData.version,
          code: nodeData.code,
          dependencies: {},
          permissions: {},
          checksum: Buffer.from(nodeData.code).toString('base64').slice(0, 32)
        }
      });

      console.log(`✅ Created pending node: ${nodeData.name} (${nodeData.id})`);
    }

    console.log('\n🎉 Test pending nodes created successfully!');
    console.log('📋 You can now test the approval system at: http://localhost:3000/admin/nodes');

  } catch (error) {
    console.error('❌ Error creating pending nodes:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createPendingNodes().catch(console.error);
