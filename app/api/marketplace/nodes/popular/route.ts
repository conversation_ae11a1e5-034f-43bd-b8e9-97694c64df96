import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { NodeCategory, NodeTier } from '@/lib/marketplace/types';

// GET /api/marketplace/nodes/popular - Get popular nodes
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    // Validate limit
    if (limit > 50) {
      return NextResponse.json(
        { error: 'Limit cannot exceed 50' },
        { status: 400 }
      );
    }

    const nodes = await prisma.nodePlugin.findMany({
      where: {},
      orderBy: [
        { downloads: 'desc' },
        { rating: 'desc' },
        { weeklyDownloads: 'desc' }
      ],
      take: limit,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        },
        _count: {
          select: {
            reviews: true,
            purchases: true
          }
        }
      }
    });

    // Transform data
    const transformedNodes = nodes.map(node => ({
      id: node.id,
      name: node.name,
      version: node.version,
      description: node.description,
      longDescription: node.longDescription,
      author: {
        name: node.author.name || 'Unknown',
        email: node.author.email,
        avatar: node.author.avatar
      },
      category: node.category as NodeCategory,
      tier: node.tier as NodeTier,
      price: node.price,
      subscriptionType: node.subscriptionType,
      tags: JSON.parse(node.tags || '[]'),
      dependencies: JSON.parse(node.dependencies || '[]'),
      permissions: JSON.parse(node.permissions || '[]'),
      icon: node.icon,
      screenshots: JSON.parse(node.screenshots || '[]'),
      downloadUrl: node.downloadUrl,
      repositoryUrl: node.repositoryUrl,
      documentationUrl: node.documentationUrl,
      verified: node.verified,
      featured: node.featured,
      rating: node.rating,
      reviewCount: node._count.reviews,
      downloads: node.downloads,
      weeklyDownloads: node.weeklyDownloads,
      lastUpdated: node.lastUpdated,
      createdAt: node.createdAt,
      compatibility: JSON.parse(node.compatibility || '{}'),
      changelog: node.changelog ? JSON.parse(node.changelog) : undefined
    }));

    return NextResponse.json(transformedNodes);

  } catch (error) {
    console.error('Error fetching popular nodes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch popular nodes' },
      { status: 500 }
    );
  }
}
