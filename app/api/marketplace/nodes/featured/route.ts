import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { NodeCategory, NodeTier } from '@/lib/marketplace/types';

// GET /api/marketplace/nodes/featured - Get featured nodes
export async function GET() {
  try {
    const nodes = await prisma.nodePlugin.findMany({
      where: {
        featured: true
      },
      orderBy: [
        { rating: 'desc' },
        { downloads: 'desc' }
      ],
      take: 12, // Limit to 12 featured nodes
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        },
        _count: {
          select: {
            reviews: true,
            purchases: true
          }
        }
      }
    });

    // Transform data
    const transformedNodes = nodes.map(node => ({
      id: node.id,
      name: node.name,
      version: node.version,
      description: node.description,
      longDescription: node.longDescription,
      author: {
        name: node.author.name || 'Unknown',
        email: node.author.email,
        avatar: node.author.avatar
      },
      category: node.category as NodeCategory,
      tier: node.tier as NodeTier,
      price: node.price,
      subscriptionType: node.subscriptionType,
      tags: JSON.parse(node.tags || '[]'),
      dependencies: JSON.parse(node.dependencies || '[]'),
      permissions: JSON.parse(node.permissions || '[]'),
      icon: node.icon,
      screenshots: JSON.parse(node.screenshots || '[]'),
      downloadUrl: node.downloadUrl,
      repositoryUrl: node.repositoryUrl,
      documentationUrl: node.documentationUrl,
      verified: node.verified,
      featured: node.featured,
      rating: node.rating,
      reviewCount: node._count.reviews,
      downloads: node.downloads,
      weeklyDownloads: node.weeklyDownloads,
      lastUpdated: node.lastUpdated,
      createdAt: node.createdAt,
      compatibility: JSON.parse(node.compatibility || '{}'),
      changelog: node.changelog ? JSON.parse(node.changelog) : undefined
    }));

    return NextResponse.json(transformedNodes);

  } catch (error) {
    console.error('Error fetching featured nodes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured nodes' },
      { status: 500 }
    );
  }
}
