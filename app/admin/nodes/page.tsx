"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, Check, X, Package } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialog<PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

export default function AdminNodesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [nodes, setNodes] = React.useState<any[]>([]);
  const [stats, setStats] = React.useState({ pending: 0, approved: 0, rejected: 0, total: 0 });
  const [processingNodeId, setProcessingNodeId] = React.useState<string | null>(null);
  const [rejectionReason, setRejectionReason] = React.useState("");
  const [rejectDialogOpen, setRejectDialogOpen] = React.useState(false);
  const [selectedNodeForReject, setSelectedNodeForReject] = React.useState<any>(null);

  // Check admin access
  React.useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      router.push("/auth/signin");
      return;
    }

    const user = session.user as any;
    if (!user.isAdmin && !user.isSuperAdmin) {
      router.push("/dashboard");
      return;
    }

    setMounted(true);
  }, [session, status, router]);

  React.useEffect(() => {
    if (!mounted) return;

    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/admin/nodes/pending?status=pending&limit=10', {
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to load nodes: ${response.status}`);
        }

        const data = await response.json();
        setNodes(data.data?.nodes || []);
        setStats(data.data?.stats || { pending: 0, approved: 0, rejected: 0, total: 0 });
      } catch (err) {
        console.error('Error loading nodes:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [mounted]);

  // Reload data function
  const reloadData = React.useCallback(async () => {
    try {
      setError(null);
      const response = await fetch('/api/admin/nodes/pending?status=pending&limit=10', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to load nodes: ${response.status}`);
      }

      const data = await response.json();
      setNodes(data.data?.nodes || []);
      setStats(data.data?.stats || { pending: 0, approved: 0, rejected: 0, total: 0 });
    } catch (err) {
      console.error('Error reloading nodes:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  }, []);

  // Handle approve node
  const handleApprove = React.useCallback(async (nodeId: string) => {
    try {
      setProcessingNodeId(nodeId);

      const response = await fetch('/api/admin/nodes/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          nodeId,
          action: 'approve'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      toast.success(`Node "${result.node.name}" approved successfully!`);

      // Reload data to update the UI
      await reloadData();
    } catch (err) {
      console.error('Error approving node:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to approve node');
    } finally {
      setProcessingNodeId(null);
    }
  }, [reloadData]);

  // Handle reject node
  const handleReject = React.useCallback(async (nodeId: string, reason: string) => {
    try {
      setProcessingNodeId(nodeId);

      const response = await fetch('/api/admin/nodes/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          nodeId,
          action: 'reject',
          rejectionReason: reason
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      toast.success(`Node "${result.node.name}" rejected successfully!`);

      // Reload data to update the UI
      await reloadData();

      // Reset reject dialog state
      setRejectDialogOpen(false);
      setSelectedNodeForReject(null);
      setRejectionReason("");
    } catch (err) {
      console.error('Error rejecting node:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to reject node');
    } finally {
      setProcessingNodeId(null);
    }
  }, [reloadData]);

  // Open reject dialog
  const openRejectDialog = React.useCallback((node: any) => {
    setSelectedNodeForReject(node);
    setRejectionReason("");
    setRejectDialogOpen(true);
  }, []);

  const getStatusBadge = React.useCallback((status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="text-green-600 border-green-600">Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="text-red-600 border-red-600">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  }, []);

  if (status === "loading" || !mounted) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/admin">Administration</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Node Approval</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
            <p className="text-muted-foreground">Loading node approval system...</p>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!session?.user) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">Administration</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Node Approval</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Node Approval System</h1>
          <p className="text-muted-foreground">Review and approve developer-submitted nodes</p>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-700">Error: {error}</p>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Package className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <Check className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <X className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Pending Nodes</CardTitle>
          <CardDescription>Nodes waiting for approval</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading nodes...</span>
            </div>
          ) : nodes.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No pending nodes found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {nodes.map((node: any) => (
                <div key={node.id} className="border rounded p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold">{node.name || 'Unnamed Node'}</h3>
                      <p className="text-sm text-muted-foreground">{node.description || 'No description'}</p>
                      <div className="mt-2">
                        {getStatusBadge(node.approvalStatus || 'pending')}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            className="bg-green-600 hover:bg-green-700"
                            disabled={processingNodeId === node.id}
                          >
                            {processingNodeId === node.id ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <Check className="h-4 w-4 mr-1" />
                            )}
                            Approve
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Approve Node</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to approve "{node.name || 'Unnamed Node'}"?
                              This will make the node available in the marketplace for all users to install.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleApprove(node.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              Approve
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>

                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 border-red-600"
                        onClick={() => openRejectDialog(node)}
                        disabled={processingNodeId === node.id}
                      >
                        {processingNodeId === node.id ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <X className="h-4 w-4 mr-1" />
                        )}
                        Reject
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
        </div>
      </SidebarInset>

      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Node</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting "{selectedNodeForReject?.name || 'this node'}".
              This feedback will help the developer improve their submission.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="rejection-reason">Rejection Reason</Label>
              <Textarea
                id="rejection-reason"
                placeholder="Please explain why this node is being rejected..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="mt-1"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setRejectDialogOpen(false);
                setSelectedNodeForReject(null);
                setRejectionReason("");
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (selectedNodeForReject && rejectionReason.trim()) {
                  handleReject(selectedNodeForReject.id, rejectionReason.trim());
                }
              }}
              disabled={!rejectionReason.trim() || processingNodeId === selectedNodeForReject?.id}
            >
              {processingNodeId === selectedNodeForReject?.id ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : (
                <X className="h-4 w-4 mr-1" />
              )}
              Reject Node
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </SidebarProvider>
  );
}
